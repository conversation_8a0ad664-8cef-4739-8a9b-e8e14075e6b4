<?php

namespace App\Http\Resources;

use App\DTOs\PodCommandResultDTO;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PodCommandResultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var PodCommandResultDTO $this */
        return [
            'stdout' => $this->stdout,
            'stderr' => $this->stderr,
            'exit_code' => $this->exitCode,
            'error' => $this->error,
            'completed' => $this->completed,
            'timeout' => $this->timeout,
            'execution_time' => round($this->executionTime, 3),
            'success' => $this->isSuccess(),
            'has_output' => $this->hasOutput(),
            'is_binary' => $this->isBinaryOutput(),
            'size_limit_exceeded' => $this->isSizeLimitExceeded(),
        ];
    }
}
